import i18n from 'i18next';

export type UILanguage = 'ko' | 'en';
// List of UI languages supported by the app
export const SUPPORTED_LANGUAGES = [
  'ko',
  'en',
] as UILanguage[];

export const getLanguageName = (language: UILanguage) => {
  switch (language) {
    case 'ko':
      return '한국어';
    case 'en':
      return 'English';
  }
};

export const getCurrentLanguageName = () => {
  return getLanguageName(getCurrentLanguage());
};

export const getCurrentLanguage = () => {
  return i18n.language as UILanguage;
};
